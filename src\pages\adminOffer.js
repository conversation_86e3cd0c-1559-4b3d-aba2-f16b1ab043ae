import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  IconButton,
  Grid,
  Button,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Dashboard from "@/components/Dashboard";
import useMediaQuery from "@mui/material/useMediaQuery";
import { breakpoints } from "../helper/mediaQueries";
import HassanaOfferPopUp from "@/components/HassanaOfferPopUp";
import { useTheme } from "@mui/material/styles";
import { useQuery, useMutation } from "@apollo/client";
import {
  GET_OFFERS,
  DELETE_OFFER,
  TOGGLE_OFFER_STATUS,
  OFFER_VIEW,
} from "../Data/Offer";
import { useSession } from "next-auth/react";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.secondary,
  borderBottom: "1px solid #e0e0e0",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  boxShadow: 5,
}));

const OffersList = () => {
  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;

  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);
  const isXXLargeScreen = useMediaQuery(xxLargeScreen);
  const isXXXLargeScreen = useMediaQuery(xxxLargeScreen);
  const isXXXXLargeScreen = useMediaQuery(xxxxLargeScreen);
  const theme = useTheme();
  const { data: session } = useSession();

  let mainDivWidth;
  if (isSmallScreen) {
    mainDivWidth = "70vw";
  } else if (isMediumScreen) {
    mainDivWidth = "70vw";
  } else if (isLargeScreen) {
    mainDivWidth = "77vw";
  } else if (isXLargeScreen) {
    mainDivWidth = "54vw";
  } else if (isXXLargeScreen) {
    mainDivWidth = "62vw";
  } else if (isXXXLargeScreen) {
    mainDivWidth = "67vw";
  } else if (isXXXXLargeScreen) {
    mainDivWidth = "70vw";
  } else {
    mainDivWidth = "70vw";
  }

  const [offers, setOffers] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openPopup, setOpenPopup] = useState(false);
  const [editOffer, setEditOffer] = useState(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [offerToDelete, setOfferToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleOpen = () => {
    setErrorMessage(""); // Reset error when opening the popup
    setEditOffer(null);
    setOpenPopup(true);
  };

  const handleClose = () => {
    setErrorMessage(""); 
    setEditOffer(null);
    setOpenPopup(false);
  };

  const handleOpenDeleteDialog = (id) => {
    setErrorMessage("");
    setOfferToDelete(id);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setErrorMessage(null); 
    setOpenDeleteDialog(false);
    setOfferToDelete(null);
  };

  // Fetch offers
  const { data, loading, error, refetch } = useQuery(GET_OFFERS, {
    variables: { user_id: session?.user?.id || "" },
    skip: !session?.user?.id,
    fetchPolicy: "network-only",
    context: {
      headers: {
        Authorization: `Bearer ${session?.accessToken || localStorage.getItem("jwtToken") || ""}`,
      },
    },
  });

  // Delete offer
  const [deleteOffer] = useMutation(DELETE_OFFER, {
    onCompleted: (data) => {
      console.log("Delete offer response:", data);
      setErrorMessage("Offer deleted successfully!");
      setTimeout(() => setErrorMessage(null), 3000);
      setIsDeleting(false);
      refetch();
    },
    onError: (err) => {
      console.error("Error deleting offer:", JSON.stringify(err, null, 2));
      setIsDeleting(false);
      const message =
        err.graphQLErrors?.[0]?.message ||
        err.networkError?.result?.errors?.[0]?.message ||
        err.message ||
        "Failed to delete offer";
      if (message.includes("Unauthorized") || message.includes("Forbidden")) {
        setErrorMessage("Authentication failed. Please log in again.");
      } else if (message.includes("not found")) {
        setErrorMessage("Offer not found. It may have already been deleted.");
      } else {
        setErrorMessage(message);
      }
    },
    update: (cache) => {
      const existingOffers = cache.readQuery({
        query: GET_OFFERS,
        variables: { user_id: session?.user?.id || "" },
      });
      if (existingOffers?.offers) {
        cache.writeQuery({
          query: GET_OFFERS,
          variables: { user_id: session?.user?.id || "" },
          data: {
            offers: existingOffers.offers.filter((offer) => offer.id !== offerToDelete),
          },
        });
      }
    },
  });

  // Toggle status
  const [toggleOfferStatus] = useMutation(TOGGLE_OFFER_STATUS, {
    onCompleted: () => {
      setErrorMessage(null); // Reset error on successful toggle
      refetch();
    },
    onError: (err) => {
      console.error("Error toggling status:", JSON.stringify(err, null, 2));
      const message =
        err.graphQLErrors?.[0]?.message ||
        err.networkError?.result?.errors?.[0]?.message ||
        err.message ||
        "Failed to toggle status";
      if (message.includes("Unauthorized") || message.includes("Forbidden")) {
        setErrorMessage("Authentication failed. Please log in again.");
      } else {
        setErrorMessage(message);
      }
    },
  });

  // Mark offer as read
  const [offerView] = useMutation(OFFER_VIEW, {
    onCompleted: () => {
      setErrorMessage(null); // Reset error on successful offer view
      refetch();
    },
    onError: (err) => {
      console.error("Error marking offer as read:", JSON.stringify(err, null, 2));
      const message =
        err.graphQLErrors?.[0]?.message ||
        err.networkError?.result?.errors?.[0]?.message ||
        err.message ||
        "Failed to mark offer as read";
      if (message.includes("Unauthorized") || message.includes("Forbidden")) {
        setErrorMessage("Authentication failed. Please log in again.");
      } else {
        setErrorMessage(message);
      }
    },
  });

  useEffect(() => {
    console.log("Data received:", data);
    if (data && data.offers) {
      setErrorMessage(null); // Reset error when new data is fetched
      const validOffers = data.offers.filter(offer => offer.id != null);
      const filteredData = validOffers.filter(
        (offer) =>
          offer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          offer.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
          offer.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setOffers(filteredData);
      setPage(0);
      if (validOffers.length < data.offers.length) {
        setErrorMessage(`Warning: ${data.offers.length - validOffers.length} invalid offers were filtered out due to missing IDs.`);
        setTimeout(() => setErrorMessage(null), 5000);
      }
    }
  }, [data, searchTerm]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (offer) => {
    setErrorMessage(null); // Reset error when initiating edit
    if (!offer?.id) {
      console.error("Invalid offer: Missing ID", JSON.stringify(offer, null, 2));
      setErrorMessage("Cannot edit offer: Missing ID. Please try again.");
      return;
    }
    console.log("Setting editOffer:", JSON.stringify(offer, null, 2));
    setEditOffer(offer);
    setOpenPopup(true);
  };

  const handleDelete = () => {
    if (!session?.accessToken) {
      setErrorMessage("Authentication token missing. Please log in again.");
      handleCloseDeleteDialog();
      return;
    }
    setIsDeleting(true);
    handleCloseDeleteDialog(); // Close modal immediately
    deleteOffer({
      variables: { id: offerToDelete },
      context: {
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
        },
      },
    });
  };

  const handleToggleStatus = (id) => {
    setErrorMessage(null); // Reset error when toggling status
    console.log("Toggling status for offer ID:", id, "Type:", typeof id);
    if (!session?.accessToken) {
      setErrorMessage("Authentication token missing. Please log in again.");
      return;
    }
    toggleOfferStatus({
      variables: { id },
      context: {
        headers: {
          Authorization: `Bearer ${session.accessToken}`,
        },
      },
    });
  };

  const handleOfferView = (offer_id) => {
    setErrorMessage(null); // Reset error when viewing offer
    console.log("Marking offer as read:", offer_id, "Type:", typeof offer_id);
    if (session?.user?.id && session?.accessToken) {
      offerView({
        variables: { offer_id, user_id: session.user.id },
        context: {
          headers: {
            Authorization: `Bearer ${session.accessToken}`,
          },
        },
      });
    } else {
      setErrorMessage("Authentication token or user ID missing. Please log in again.");
    }
  };

  if (loading) return <Typography>Loading...</Typography>;
  if (error) return <Typography>Error: {error.message}</Typography>;

  return (
    <Dashboard>
      <Box
        sx={{
          margin: "auto",
          height: "90vh",
          overflowY: "auto",
          "&::-webkit-scrollbar": { width: 0 },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
          padding: 2,
          background: theme.palette.background.secondary,
        }}
      >
        <Grid
          container
          spacing={isSmallScreen ? 1 : 3}
        >
          <Grid item xs={12}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 1,
                mt: 4,
              }}
            >
              <Typography
                variant="h5"
                color="#1B3745"
                fontWeight="700"
                borderBottom="1px solid #DADADA"
                pb="20px"
                pr="75px"
                sx={{
                  color: theme.palette.background.secondary,
                }}
              >
                Hassana Offers
              </Typography>
              <Box>
                <Button
                  variant="contained"
                  disableElevation
                  sx={{
                    backgroundColor: "#A665E1 !important",
                    color: "#fff !important",
                    paddingX:"12px",
                    paddingY:"8px",
                    borderRadius: "10px",
                    "&:hover": {
                      backgroundColor: "#944FCB !important",
                      color: "#fff !important",
                    },
                    boxShadow: "none !important",
                  }}
                  
                  onClick={handleOpen}
                >
                  Add Offer
                </Button>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={12}>
            {errorMessage && (
              <Alert
                severity={errorMessage.includes("successfully") ? "success" : "error"}
                sx={{ mb: 2 }}
                onClose={() => setErrorMessage(null)}
              >
                {errorMessage}
              </Alert>
            )}
            {isDeleting && (
              <Box sx={{ display: "flex", justifyContent: "center", my: 2 }}>
                <CircularProgress size={24} />
              </Box>
            )}
            <StyledPaper
              sx={{
                width: "100%",
                overflowX: "auto",
                minWidth: "90%",
                padding: 1,
                background: theme.palette.text.back,
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mx="20px"
                my="5px"
                sx={{ color: theme.palette.text.back }}
              >
                <Typography variant="p" color="#1B3745" fontWeight="700"
                  sx={{ color: theme.palette.background.secondary }}
                >
                  ({offers.length}) Offers
                </Typography>
                <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                  <TextField
                    variant="outlined"
                    placeholder="Search"
                    size="small"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{
                      width: "300px",
                      backgroundColor: theme.palette.mode === 'light' ? '#dadada' : theme.palette.grey[700],
                      borderRadius: "4px",
                      "& fieldset": {
                        border: "none",
                      },
                      "& input": {
                        fontSize: "12px",
                        color: theme.palette.text.primary,
                        "&::placeholder": {
                          color: "#000000",
                          opacity: 1,
                        },
                      },
                    }}
                  />
                </Box>
              </Box>
              <Table
                sx={{ background: theme.palette.background.primary }}
              >
                <TableHead>
                  <TableRow>
                    <StyledTableCell sx={{ fontSize: "16px", color: theme.palette.background.secondary }}>
                      Title
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{ fontSize: "16px", color: theme.palette.background.secondary }}
                      align="left"
                    >
                      Contact
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{ fontSize: "16px", color: theme.palette.background.secondary }}
                      align="left"
                    >
                      Code
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{ fontSize: "16px", color: theme.palette.background.secondary }}
                      align="left"
                    >
                      Description
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{ fontSize: "16px", color: theme.palette.background.secondary }}
                      align="left"
                    >
                      Expire
                    </StyledTableCell>
                    <StyledTableCell
                      sx={{ fontSize: "16px", color: theme.palette.background.secondary }}
                      align="left"
                    >
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        Actions
                        
                      </Box>
                    </StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {offers
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((offer) => (
                      <StyledTableRow
                        key={offer.id}
                        onClick={() => handleOfferView(offer.id)}
                      >
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            color: theme.palette.background.secondary,
                            maxWidth: "110px",
                          }}
                          component="th"
                          scope="row"
                        >
                          {offer.name}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            color: theme.palette.background.secondary,
                            maxWidth: "115px",
                          }}
                          align="left"
                        >
                          {offer.contact_information || "-"}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            color: theme.palette.background.secondary,
                            maxWidth: "120px",
                          }}
                          align="left"
                        >
                          {offer.code}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            color: theme.palette.background.secondary,
                            maxWidth: "210px",
                          }}
                          align="left"
                        >
                          {offer.description || "-"}
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: "12px",
                            
                          }}
                          align="left"
                        >
                          {(() => {
                            const isExpired = new Date(offer.expiry_date) < new Date();
                            const dateStr = new Date(offer.expiry_date).toLocaleDateString();
                            return isExpired ? `${dateStr} ` : dateStr;
                          })()}
                        </TableCell>
                        <TableCell align="left">
                          <Box sx={{ display: "flex", gap: 0.001, alignItems: "center" }}>
                            {/* Status Dot with Click Handler */}
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleStatus(offer.id);
                              }}
                              disabled={isDeleting}
                              sx={{
                                padding: "8px",
                                borderRadius: "8px",
                                backgroundColor: (() => {
                                  const isExpired = new Date(offer.expiry_date) < new Date();
                                  if (isExpired) {
                                    return "rgba(244, 67, 54, 0.1)"; // Light red background for expired
                                  } else if (offer.status) {
                                    return "rgba(76, 175, 80, 0.1)"; // Light green background for active
                                  } else {
                                    return "rgba(244, 67, 54, 0.1)"; // Light red background for inactive
                                  }
                                })(),
                                "&:hover": {
                                  backgroundColor: (() => {
                                    const isExpired = new Date(offer.expiry_date) < new Date();
                                    if (isExpired) {
                                      return "rgba(244, 67, 54, 0.2)"; // Darker red on hover
                                    } else if (offer.status) {
                                      return "rgba(76, 175, 80, 0.2)"; // Darker green on hover
                                    } else {
                                      return "rgba(244, 67, 54, 0.2)"; // Darker red on hover
                                    }
                                  })(),
                                  transform: "scale(1.05)"
                                }
                              }}
                            >
                              <Box
                                sx={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: "50%",
                                  backgroundColor: (() => {
                                    // Check if offer is expired
                                    const isExpired = new Date(offer.expiry_date) < new Date();

                                    if (isExpired) {
                                      return "#f44336"; // Red for expired
                                    } else if (offer.status) {
                                      return "#4caf50"; // Green for active
                                    } else {
                                      return "#f44336"; // Red for inactive
                                    }
                                  })(),
                                  cursor: "pointer",
                                  transition: "all 0.2s ease",
                                  border: "2px solid",
                                  borderColor: (() => {
                                    const isExpired = new Date(offer.expiry_date) < new Date();
                                    if (isExpired) {
                                      return "rgba(244, 67, 54, 0.3)"; // Light red border
                                    } else if (offer.status) {
                                      return "rgba(76, 175, 80, 0.3)"; // Light green border
                                    } else {
                                      return "rgba(244, 67, 54, 0.3)"; // Light red border
                                    }
                                  })(),
                                  "&:hover": {
                                    transform: "scale(1.1)",
                                    boxShadow: (() => {
                                      const isExpired = new Date(offer.expiry_date) < new Date();
                                      if (isExpired) {
                                        return "0 2px 8px rgba(244, 67, 54, 0.3)"; // Red shadow
                                      } else if (offer.status) {
                                        return "0 2px 8px rgba(76, 175, 80, 0.3)"; // Green shadow
                                      } else {
                                        return "0 2px 8px rgba(244, 67, 54, 0.3)"; // Red shadow
                                      }
                                    })()
                                  }
                                }}
                                title={(() => {
                                  const isExpired = new Date(offer.expiry_date) < new Date();
                                  if (isExpired) {
                                    return "Expired";
                                  } else if (offer.status) {
                                    return "Active - Click to deactivate";
                                  } else {
                                    return "Inactive - Click to activate";
                                  }
                                })()}
                              />
                            </IconButton>
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(offer);
                              }}
                              disabled={isDeleting}
                            >
                              <Box
                                component="img"
                                src="/icons/editIcon.svg"
                                alt="Edit"
                                sx={{ width: 21, height: 21 }}
                              />
                            </IconButton>
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                handleOpenDeleteDialog(offer.id);
                              }}
                              disabled={isDeleting}
                            >
                              <Box
                                component="img"
                                src="/icons/deletIcon.svg"
                                alt="Delete"
                                sx={{ width: 21, height: 21 }}
                              />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </StyledTableRow>
                    ))}
                </TableBody>
              </Table>
              <TablePagination
                sx={{ color: theme.palette.text.primary }}
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={offers.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </StyledPaper>
          </Grid>
        </Grid>
      </Box>
      <HassanaOfferPopUp
        open={openPopup}
        handleClose={handleClose}
        offer={editOffer}
        refetchOffers={refetch}
        
      />
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" sx={{ color: "black" }}>
          {"Confirm Delete"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Are you sure you want to delete this offer? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={isDeleting}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" autoFocus disabled={isDeleting}>
            {isDeleting ? "Deleting..." : "OK"}
          </Button>
        </DialogActions>
      </Dialog>
    </Dashboard>
  );
};

export default OffersList;