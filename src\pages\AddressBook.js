import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Avatar,
  Menu,
  MenuItem,
  IconButton,
  Grid,
  Alert,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Dashboard from "@/components/Dashboard";
import StarIcon from "@mui/icons-material/Star";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import useMediaQuery from "@mui/material/useMediaQuery";
import { breakpoints } from "../helper/mediaQueries";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import { getAllUsers } from "../Data/User";
import { useTheme } from "@mui/system";

// Styled Components
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontWeight: "bold",
  backgroundColor: theme.palette.background.secondary,
  borderBottom: "1px solid #e0e0e0",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  overflow: "hidden",
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const StyledBox = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
  boxShadow: 5,
}));

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
  backgroundColor: theme.palette.background.secondary,
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const EmployeeList = () => {
  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);
  const isXXLargeScreen = useMediaQuery(xxLargeScreen);
  const isXXXLargeScreen = useMediaQuery(xxxLargeScreen);
  const isXXXXLargeScreen = useMediaQuery(xxxxLargeScreen);
  const baseurl= 'http://localhost:3009/v1/'
  let mainDivWidth;
  if (isSmallScreen) mainDivWidth = "70vw";
  else if (isMediumScreen) mainDivWidth = "70vw";
  else if (isLargeScreen) mainDivWidth = "77vw";
  else if (isXLargeScreen) mainDivWidth = "56vw";
  else if (isXXLargeScreen) mainDivWidth = "62vw";
  else if (isXXXLargeScreen) mainDivWidth = "67vw";
  else if (isXXXXLargeScreen) mainDivWidth = "70vw";
  else mainDivWidth = "70vw";

  const { data: session } = useSession();
  const router = useRouter();

  const [employees, setEmployees] = useState([]);
  const [filteredEmployees, setFilteredEmployees] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const [sortOrder, setSortOrder] = useState("Highest");
  const [errorMessage, setErrorMessage] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      setErrorMessage(null);

      try {
        const token = session?.accessToken || localStorage.getItem("jwtToken");
        console.log("Fetching users with token:", token);

        const result = await getAllUsers(token, page + 1, rowsPerPage);

        if (result?.error) {
          setErrorMessage(`Error: ${result.error} (Status: ${result.status || "unknown"})`);
          setEmployees([]);
          setTotalCount(0);
        } else {
          const mappedData = result.data.map((user, index) => ({
            id: user.id,
            name: user.name,
            designation: user.designation || user.role || "Employee",
            department: user.department || "Unknown",
            email: user.user_principal_name || user.email || "<EMAIL>",
            extension: user.extension || "", // Add extension field
            starScore: parseFloat(user.monthly_rating) || 0,
            showDot: user.new_joiner === "true" || user.new_joiner === true,
            thisYear: parseFloat(user.yearly_rating),
            profile: user.profile || null
          }));
          setEmployees(mappedData);
          setTotalCount(result.meta?.totalCount || 0);
          console.log("=== AddressBook Debug ===");
          console.log("Raw API result:", result);
          console.log("Sample user data:", result.data?.[0]);
          console.log("Extension field in first user:", result.data?.[0]?.extension);
          console.log("Mapped employees:", mappedData);
          console.log("Sample mapped employee:", mappedData[0]);
        }
      } catch (error) {
        setErrorMessage("Failed to fetch users. Please try again later.");
        setEmployees([]);
        setTotalCount(0);
        console.error("Error fetching users:", error);
      }
      setLoading(false);
    };

    fetchUsers();
  }, [session, page, rowsPerPage]);

  useEffect(() => {
    // Create a copy of employees to avoid mutating the original
    let filtered = [...employees];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (employee) =>
          employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.designation?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.department?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          employee.extension?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      // Reset to first page when searching
      if (page !== 0) {
        setPage(0);
      }
    }

    // Sort the filtered array
    const sorted = [...filtered].sort((a, b) => {
      const scoreA = a.starScore || 0;
      const scoreB = b.starScore || 0;
      return sortOrder === "Highest" ? scoreB - scoreA : scoreA - scoreB;
    });

    console.log("Sort order:", sortOrder, "Sorted employees:", sorted);
    setFilteredEmployees(sorted);
  }, [employees, searchTerm, sortOrder]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSortClick = (event) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortClose = (order) => {
    if (order) {
      setSortOrder(order);
    }
    setSortAnchorEl(null);
  };

  if (loading) return <Typography sx={{ textAlign: "center", mt: 4 }}>Loading...</Typography>;

  return (
    <Dashboard>
      <StyledBox
        sx={{
          width: mainDivWidth,
          margin: "auto",
          height: "90vh",
          overflowY: "auto",
          "&::-webkit-scrollbar": { width: 0 },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
          padding: 2,
          marginTop: 3,
          cursor: "pointer"
        }}
      >
        <Grid container spacing={isSmallScreen ? 1 : 3}>
          <Grid item xs={12}>
            <Box
              sx={{
                display: { xs: "block", md: "flex" },
                justifyContent: { md: "space-between" },
                gap: 2,
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6" fontWeight="700" mb="15px">
                All Employees ({totalCount})
              </Typography>
              <StyledBox sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <TextField
                  variant="outlined"
                  placeholder="Search"
                  size="small"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{ width: "200px", borderRadius: "4px" }}
                />
                <StyledBox
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    borderRadius: "4px",
                    padding: "4px 8px",
                  }}
                >
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    Sorted by {sortOrder}
                  </Typography>
                  <IconButton onClick={handleSortClick}>
                    <ArrowDropDownIcon />
                  </IconButton>
                  <Menu
                    anchorEl={sortAnchorEl}
                    open={Boolean(sortAnchorEl)}
                    onClose={() => handleSortClose()}
                  >
                    <StyledMenuItem
                      sx={{ "&:hover": { backgroundColor: "#A0A0A0" } }}
                      onClick={() => handleSortClose("Highest")}
                    >
                      Highest
                    </StyledMenuItem>
                    <StyledMenuItem
                      sx={{ "&:hover": { backgroundColor: "#A0A0A0" } }}
                      onClick={() => handleSortClose("Lowest")}
                    >
                      Lowest
                    </StyledMenuItem>
                  </Menu>
                </StyledBox>
              </StyledBox>
            </Box>
          </Grid>
          <Grid item xs={12}>
            {errorMessage && (
              <Alert severity="warning" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
                {errorMessage}
              </Alert>
            )}
            {filteredEmployees.length === 0 && !errorMessage && (
              <Alert severity="info" sx={{ mb: 2 }}>
                No employees found.
              </Alert>
            )}
            <StyledPaper sx={{ width: "100%", overflowX: "auto", minWidth: "90%", padding: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <StyledTableCell>Employee Name</StyledTableCell>
                    <StyledTableCell align="left">Designation</StyledTableCell>
                    <StyledTableCell align="left">Department</StyledTableCell>
                    <StyledTableCell align="left">Email</StyledTableCell>
                    {/* <StyledTableCell align="left">Extension</StyledTableCell> */}
                    <StyledTableCell align="left">Star Scores</StyledTableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(searchTerm ?
                    filteredEmployees.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage) :
                    filteredEmployees)
                    .map((employee) => (
                      <StyledTableRow
                        key={employee.id}
                        onClick={() =>
                          router.push({
                            pathname: "/Employeedetail",
                            query: { userId: employee.id, employee: JSON.stringify(employee) },
                          })
                        }
                        sx={{ cursor: "pointer" }}
                      >
                        <TableCell
                          component="th"
                          scope="row"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                             {employee.profile ? (
                                <img
                                  src={employee.profile.startsWith('http') ? employee.profile : baseurl + employee.profile}
                                  alt={employee.name}
                                  style={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: "50%",
                                    objectFit: "cover"
                                  }}
                                  onError={(e) => {
                                    e.target.src = "/AvatarBlank.jpg";
                                  }}
                                />
                              ) : (
                                <Avatar src="/AvatarBlank.jpg" alt={employee.name} />
                              )}
                              {employee.name}
                            </Box>
                        </TableCell>
                        <TableCell
                          align="left"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          {employee.designation}
                        </TableCell>
                        <TableCell
                          align="left"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            {employee.department}
                          </Box>
                        </TableCell>
                        <TableCell
                          align="left"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          {employee.email}
                        </TableCell>
                        {/* <TableCell
                          align="left"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          {employee.extension || "N/A"}
                        </TableCell> */}
                        <TableCell
                          align="left"
                          sx={{ whiteSpace: "nowrap", textOverflow: "ellipsis", overflow: "hidden" }}
                        >
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            <StarIcon sx={{ color: "#FFD700" }} />
                            <Typography variant="body2">{employee.starScore}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              this month
                            </Typography>
                          </Box>
                        </TableCell>
                      </StyledTableRow>
                    ))}
                </TableBody>
              </Table>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={searchTerm ? filteredEmployees.length : totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Showing"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}
              />
            </StyledPaper>
          </Grid>
        </Grid>
      </StyledBox>
    </Dashboard>
  );
};

export default EmployeeList;