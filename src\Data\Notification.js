import { gql } from "@apollo/client";

export const getNotifications = gql`
  query{
    notifications {
        id
        notification
        createdAt
       
    }
}
`;

export const mutationAddNotificationView = gql`
  mutation AddNotificationView($notificationId: ID!, $userId: ID!) {
    addNotificationView(notificationId: $notificationId, userId: $userId) {
      id
    }
  }
`;


export const getNewNotifications = gql`
  query NewNotificationsForUser($userId: ID!) {
    newNotificationsForUser(id: $userId) {
      id
      notification
      createdAt
    }
  }
`;


export const mutationCreateNotification = gql`
mutation CreateNotification(
  $notification: String!
#   $status: Boolean!
) {
  createNotification(createNotificationInput: {
    notification: $notification
    # status: $status
  }) {
    id,
    notification,
    # status
  }
}
`;

export const mutationUpdateNotification = gql`
  mutation UpdateNotification($id: ID!, $updateNotificationInput: UpdateNotificationInput!) {
    updateNotification(id: $id, updateNotificationInput: $updateNotificationInput) {
      id
      notification
      createdAt
      views {
        id
        name
        email
      }
    }
  }
`;

export const mutationRemoveNotification = gql`
  mutation RemoveNotification($id: ID!) {
    removeNotification(id: $id) {
        notification,
    }
  }
`;

export const getUnseenNotificationsCount = gql`
  query UnseenNotificationsCount($userId: ID!) {
    unseenNotificationsCount(userId: $userId)
  }
`;

export const mutationMarkAllNotificationsAsSeen = gql`
  mutation MarkAllNotificationsAsSeen($userId: ID!) {
    markAllNotificationsAsSeen(userId: $userId)
  }
`;
