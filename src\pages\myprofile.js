import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, TextField, Button, Divider, DialogActions, Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import Dashboard from "@/components/Dashboard";
import Rate from '../../public/images/rate.png';
import Image from 'next/image';
import { useSession } from "next-auth/react";
import { FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { baseUrl } from '@/Data/ApolloClient';
import { getAllUsers, updateUser } from '@/Data/User';

const ProfileEdit = () => {
  const { data: session, status } = useSession();
  const theme = useTheme();
  const [profileData, setProfileData] = useState({
    employmentId: '',
    username: '',
    designation: '',
    activity: '',
    email: '',
    extension: '',
  });
  const [profileImage, setProfileImage] = useState("/AvatarBlank.jpg");
  const [profileImageKey, setProfileImageKey] = useState(Date.now());
  const [isCustomImage, setIsCustomImage] = useState(false);
  const [showAvatars, setShowAvatars] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [ratingCount, setRatingCount] = useState(0);
  const avatars = [
    '/gAvatar.svg',
    '/gAvatar2.jpg',
    '/pAvatar.avif',
    '/pAvatar2.avif',
  ];
  const baseURL = "https://hassana-api.360xpertsolutions.com/v1/";
  
  const isAdmin = session?.user?.role === "ADMIN";

  // Helper function to validate and fix image URLs
  const validateImageUrl = (url) => {
    if (!url) return "/AvatarBlank.jpg";

    // If it's a local avatar, return as-is
    if (url.startsWith('/') && avatars.includes(url)) {
      return url;
    }

    // If it's a full HTTP URL, check if it's accessible
    if (url.startsWith('http')) {
      // Allow server-provided URLs even if they include localhost for testing
      return url;
    }

    // If it's a relative path, assume it's a server-provided custom image
    if (!url.startsWith('/')) {
      return baseURL + url;
    }

    // Default fallback
    return "/AvatarBlank.jpg";
  };

  const fetchProfile = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      console.log("Missing session data:", {
        userId: session?.user?.id,
        hasToken: !!session?.accessToken
      });
      setErrorMessage("Please log in to view your profile.");
      return;
    }

    console.log("Fetching profile for user ID:", session.user.id);
    setLoading(true);
    setErrorMessage(null);

    try {
      console.log("Calling getAllUsers with token:", session.accessToken?.substring(0, 20) + "...");
      const result = await getAllUsers(session.accessToken);
      console.log("getAllUsers result:", result);

      if (result?.error) {
        console.error("API Error:", result.error);
        setErrorMessage(`Failed to fetch profile: ${result.error}`);
        return;
      }

      if (!result?.data) {
        console.error("No data in result:", result);
        setErrorMessage("No user data received from server");
        return;
      }

      console.log("All users data:", result.data);
      console.log("Looking for user with ID:", session.user.id);

      const user = result.data?.find((e) => {
        console.log("Comparing:", e.id, "with", session.user.id, "types:", typeof e.id, typeof session.user.id);
        return e.id == session.user.id;
      });

      console.log("Found user:", user);

      if (user) {
        console.log("Setting profile data for user:", user);
        const profileData = {
          employmentId: user.employmentId || user.id || '',
          username: user.name || '',
          designation: user.designation || '',
          activity: user.activity || '',
          email: user.email || user.user_principal_name || '',
          extension: user.extension || '',
        };
        console.log("Profile data to set:", profileData);

        // Handle profile image URL construction
        let profileImageUrl = "/AvatarBlank.jpg";
        let isCustom = false;

        if (user.profile) {
          console.log("User profile field:", user.profile);

          // Check if it's a full URL
          if (user.profile.startsWith('http')) {
            profileImageUrl = user.profile;
            isCustom = true;
          }
          // Check if it's a predefined avatar
          else if (avatars.includes(user.profile) || avatars.includes('/' + user.profile)) {
            profileImageUrl = user.profile.startsWith('/') ? user.profile : '/' + user.profile;
            isCustom = false;
          }
          // It's a custom uploaded image path
          else {
            profileImageUrl = baseURL + user.profile;
            isCustom = true;
          }
        }

        console.log("Final profile image URL:", profileImageUrl);
        console.log("Is custom image:", isCustom);

        // Validate the image URL before setting it
        const validatedImageUrl = validateImageUrl(profileImageUrl);
        if (validatedImageUrl !== profileImageUrl) {
          console.log("Image URL was invalid, using fallback:", validatedImageUrl);
          profileImageUrl = validatedImageUrl;
          isCustom = false;
        }

        setProfileData(profileData);
        setProfileImage(profileImageUrl);
        setProfileImageKey(Date.now());
        setIsCustomImage(isCustom);

        // Additional debugging
        console.log("Profile image set to:", profileImageUrl);
        console.log("Available avatars:", avatars);
      } else {
        console.error("User not found in data array");
        console.log("Available user IDs:", result.data?.map(u => ({ id: u.id, name: u.name })));

        console.log("Trying fallback: using session data");
        if (session?.user) {
          const sessionUser = session.user;
          console.log("Session user data:", sessionUser);

          const fallbackProfileData = {
            employmentId: sessionUser.id || '',
            username: sessionUser.username || sessionUser.name || '',
            designation: '',
            activity: '',
            email: sessionUser.email || '',
            extension: '',
          };
          console.log("Fallback profile data:", fallbackProfileData);

          setProfileData(fallbackProfileData);
          setProfileImage("/AvatarBlank.jpg");
          setProfileImageKey(Date.now());
          setIsCustomImage(false);
          setErrorMessage("Using basic profile data. Some information may be missing.");
        } else {
          setErrorMessage("User not found in the system.");
        }
      }

      try {
        console.log("Fetching user reviews...");
        const reviewsResponse = await fetch(`${baseUrl}/v1/user-reviews`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${session.accessToken}`,
          },
        });

        if (reviewsResponse.ok) {
          const reviewsResult = await reviewsResponse.json();
          console.log("Reviews result:", reviewsResult);

          if (reviewsResult.status && reviewsResult.data) {
            const userReviews = reviewsResult.data.filter((review) => review.user_id == session.user.id);
            console.log("User reviews found:", userReviews.length);
            setRatingCount(userReviews.length);
          } else {
            console.warn("Reviews API returned unexpected format:", reviewsResult);
            setRatingCount(0);
          }
        } else {
          console.warn("Reviews API failed:", reviewsResponse.status, reviewsResponse.statusText);
          setRatingCount(0);
        }
      } catch (reviewError) {
        console.warn("Error fetching reviews (non-critical):", reviewError);
        setRatingCount(0);
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      setErrorMessage("Failed to fetch profile data: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      fetchProfile();
    }
  }, [session, status]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prevData) => ({
      ...prevData,
      [name]: value || '',
    }));
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const imageURL = URL.createObjectURL(file);
      setProfileImage(imageURL);
      setUploadedFile(file);
      setIsCustomImage(true);
      setShowAvatars(false);
      setProfileImageKey(Date.now());
    }
  };

  const handleAvatarClick = (avatar) => {
    console.log("Avatar selected:", avatar);
    setProfileImage(avatar);
    setUploadedFile(null);
    setIsCustomImage(false);
    setShowAvatars(false);
    setProfileImageKey(Date.now());
    console.log("Avatar selection state updated - profileImage:", avatar, "isCustomImage:", false);
  };

  const handleUpdate = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      setErrorMessage("Please log in to update your profile.");
      return;
    }

    setLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      // Determine the correct profile value to send
      let profileValue;
      let isImageChanged = false;

      if (isCustomImage && uploadedFile) {
        // Custom uploaded image file
        profileValue = uploadedFile;
        isImageChanged = true;
        console.log("Sending custom uploaded file");
      } else if (profileImage) {
        // Clean the URL - remove any base URL and send only the path
        if (profileImage.startsWith('http')) {
          // Remove the base URL part, keep only the path
          profileValue = profileImage.replace(/^https?:\/\/[^/]+/, '');
        } else {
          // It's already a path (like /gAvatar.svg)
          profileValue = profileImage;
        }
        isImageChanged = true;
        console.log("Sending cleaned profile path:", profileValue);
      } else {
        // Fallback
        profileValue = "/AvatarBlank.jpg";
        isImageChanged = false;
        console.log("Using fallback avatar");
      }

      const formData = {
        id: session.user.id,
        name: profileData.username,
        designation: profileData.designation,
        email: profileData.email,
        extension: profileData.extension,
        activity: profileData.activity || '',
        role: isAdmin ? 'Admin' : 'User',
        status: 'active',
        profile: baseUrprofileValue,
      };

      console.log("Update formData:", formData);
      console.log("Profile value being sent:", formData.profile);
      console.log("isCustomImage:", isCustomImage);
      console.log("uploadedFile:", uploadedFile);
      console.log("isImageChanged:", isImageChanged);

      // Call updateUser with isImageChanged flag
      const result = await updateUser(formData, isImageChanged, session.accessToken);
      console.log("Update result:", result);

      if (result?.error) {
        throw new Error(result.error);
      }

      if (result.code === 200 && result.data) {
        console.log("Update success, server data:", result.data);

        // Handle profile image update based on server response
        let updatedImageUrl = "/AvatarBlank.jpg";
        let isCustom = false;

        if (result.data.profile) {
          if (result.data.profile.startsWith('http')) {
            updatedImageUrl = result.data.profile;
            isCustom = true;
          } else if (avatars.includes(result.data.profile) || avatars.includes('/' + result.data.profile)) {
            updatedImageUrl = result.data.profile.startsWith('/') ? result.data.profile : '/' + result.data.profile;
            isCustom = false;
          } else {
            updatedImageUrl = baseURL + result.data.profile;
            isCustom = true;
          }

          // Validate the server response
          const validatedUrl = validateImageUrl(updatedImageUrl);
          if (validatedUrl !== updatedImageUrl) {
            console.log("Server returned invalid image URL, using fallback:", validatedUrl);
            updatedImageUrl = validatedUrl;
            isCustom = false;
          }
        }

        console.log("Updating UI with image URL:", updatedImageUrl);
        setProfileImage(updatedImageUrl);
        setProfileImageKey(Date.now());
        setIsCustomImage(isCustom);
        setUploadedFile(null); // Clear uploaded file after successful update

        setSuccessMessage('Profile updated successfully!');
      } else {
        throw new Error(`Invalid response from server: ${JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setErrorMessage(`Failed to update profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      setErrorMessage("Please log in to reset your profile.");
      return;
    }

    setLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      await fetchProfile();
      setSuccessMessage("Profile reset to current data.");
    } catch (error) {
      console.error("Error resetting profile:", error);
      setErrorMessage("Failed to reset profile: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Typography variant="h6" textAlign="center">
            Loading session...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: '10px',
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
          padding: '20px',
          maxWidth: 900,
          margin: 'auto',
          overflowY: 'auto',
          maxHeight: '90vh',
          marginTop: '1px',
          '&::-webkit-scrollbar': { display: 'none' },
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
        }}
      >
        <Typography variant="h5" fontWeight="600" color="text.primary" gutterBottom>
          Edit and Update your profile
        </Typography>
        <Typography variant="h4" fontWeight="700" color="text.primary">
          My profile
        </Typography>

        <Divider sx={{ marginY: 2 }} />

        {errorMessage && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
            {errorMessage}
          </Alert>
        )}
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        )}
        {loading && (
          <Typography variant="h6" textAlign="center" sx={{ my: 2 }}>
            Loading...
          </Typography>
        )}

        <Grid container spacing={20}>
          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            <Box sx={{
              position: 'relative',
              width: 300,
              height: 350,
              borderRadius: '10px',
              overflow: 'hidden',
              marginX: 'auto',
              marginY: '10px',
              border: '2px solid #e0e0e0'
            }}>
              {isCustomImage ? (
                <img
                  src={profileImage}
                  alt="Profile"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '8px'
                  }}
                  onError={(e) => {
                    console.log("Custom image failed to load:", profileImage);
                    console.log("Switching to fallback avatar");
                    e.target.src = "/AvatarBlank.jpg";
                    setIsCustomImage(false);
                    setProfileImage("/AvatarBlank.jpg");
                    setProfileImageKey(Date.now());
                  }}
                />
              ) : (
                <Image
                  src={profileImage}
                  alt="Profile Avatar"
                  fill
                  style={{ objectFit: 'cover' }}
                  key={profileImageKey}
                  onError={() => {
                    console.log("Next.js Image failed to load:", profileImage);
                    console.log("Switching to fallback avatar");
                    setProfileImage("/AvatarBlank.jpg");
                    setProfileImageKey(Date.now());
                  }}
                />
              )}
            </Box>
            <DialogActions
              sx={{
                justifyContent: 'flex-start',
                flexDirection: 'row',
                gap: 1,
                padding: 0,
                marginY: 1,
              }}
            >
              <Button
                variant="contained"
                color="secondary"
                component="label"
                sx={{
                  backgroundColor: "#A665E1 !important",
                  fontSize: '12px',
                  fontWeight: 800,
                  width: isCustomImage ? '300px' : '300px',
                  padding: '6px 12px',
                  textTransform: 'none',
                  lineHeight: 1.5,
                }}
                disabled={loading}
              >
                Change Profile
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleImageUpload}
                />
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => setShowAvatars((prev) => !prev)}
                sx={{
                  fontSize: '12px',
                  fontWeight: 800,
                  width: isCustomImage ? '300px' : '300px',
                  padding: '6px 12px',
                  textTransform: 'none',
                  lineHeight: 1.5,
                }}
                disabled={loading}
              >
                Choose Avatar
              </Button>
            </DialogActions>

            {showAvatars && (
              <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 2 }}>
                {avatars.map((avatar, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      overflow: 'hidden',
                      cursor: 'pointer',
                      marginX: 1,
                    }}
                    onClick={() => handleAvatarClick(avatar)}
                  >
                    <Image src={avatar} alt={`Avatar ${index + 1}`} width={50} height={50} style={{ objectFit: 'cover' }} />
                  </Box>
                ))}
              </Box>
            )}
          </Grid>

          <Grid item xs={12} md={8}>
            <Box component="form">
              <TextField
                label="Employment ID"
                name="employmentId"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.employmentId || ''}
                onChange={handleInputChange}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="User name"
                name="username"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.username || ''}
                onChange={handleInputChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="Designation"
                name="designation"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.designation || ''}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              {isAdmin ? (
                <FormControl fullWidth sx={{ my: 2 }} disabled={loading}>
                  <InputLabel>Activity</InputLabel>
                  <Select
                    name="activity"
                    value={profileData.activity || ''}
                    label="Activity"
                    onChange={handleInputChange}
                  >
                    <MenuItem value="new_joiner">New Joiner</MenuItem>
                    <MenuItem value="cultural_ambassador">Cultural Ambassador</MenuItem>
                    <MenuItem value="">None</MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  label="Activity"
                  name="activity"
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  value={profileData.activity || ''}
                  disabled={true}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              )}
              <TextField
                label="Email"
                name="email"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.email || ''}
                onChange={handleInputChange}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="Extension"
                name="extension"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.extension || ''}
                onChange={handleInputChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-start',
                marginTop: 3,
                gap: 1,
              }}
            >
              <Typography sx={{ fontSize: '20px', fontWeight: 600 }}>
                {ratingCount}
              </Typography>
              <Image
                src={Rate}
                alt="Decoration"
                width={30}
                height={25}
              />
            </Box>
          </Grid>
        </Grid>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: 3,
            gap: 1,
          }}
        >
          <Button
            variant="outlined"
            color="secondary"
            onClick={handleReset}
            disabled={loading}
          >
            Reset
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{ backgroundColor: "#A665E1 !important" }}
            onClick={handleUpdate}
            disabled={loading}
          >
            Update
          </Button>
        </Box>
      </Box>
    </Dashboard>
  );
};

export default ProfileEdit; 