import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, TextField, Button, Avatar, Divider, DialogActions, Alert } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import Dashboard from "@/components/Dashboard";
import Rate from '../../public/images/rate.png';
import Image from 'next/image';
import { useSession } from "next-auth/react";
import { FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { baseUrl } from '@/Data/ApolloClient';
import { getAllUsers, updateUser } from '@/Data/User';

const ProfileEdit = () => {
  const { data: session, status } = useSession();
  const theme = useTheme();
  const [profileData, setProfileData] = useState({
    employmentId: '',
    username: '',
    designation: '',
    activity: '',
    email: '',
    extension: '',
  });
  const [profileImage, setProfileImage] = useState("/AvatarBlank.jpg");
  const [profileImageKey, setProfileImageKey] = useState(Date.now());
  const [isCustomImage, setIsCustomImage] = useState(false);
  const [showAvatars, setShowAvatars] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [ratingCount, setRatingCount] = useState(0);
  const avatars = [
    '/gAvatar.svg',
    '/gAvatar2.jpg',
    '/pAvatar.avif',
    '/pAvatar2.avif',
  ];
  const baseURL = "https://hassana-api.360xpertsolutions.com/v1/";
  const isAdmin = session?.user?.role === "ADMIN";

  const fetchProfile = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      console.log("Missing session data:", {
        userId: session?.user?.id,
        hasToken: !!session?.accessToken
      });
      setErrorMessage("Please log in to view your profile.");
      return;
    }

    console.log("Fetching profile for user ID:", session.user.id);
    setLoading(true);
    setErrorMessage(null);

    try {
      console.log("Calling getAllUsers with token:", session.accessToken?.substring(0, 20) + "...");
      const result = await getAllUsers(session.accessToken);
      console.log("getAllUsers result:", result);

      if (result?.error) {
        console.error("API Error:", result.error);
        setErrorMessage(`Failed to fetch profile: ${result.error}`);
        return;
      }

      if (!result?.data) {
        console.error("No data in result:", result);
        setErrorMessage("No user data received from server");
        return;
      }

      console.log("All users data:", result.data);
      console.log("Looking for user with ID:", session.user.id);

      const user = result.data?.find((e) => {
        console.log("Comparing:", e.id, "with", session.user.id, "types:", typeof e.id, typeof session.user.id);
        return e.id == session.user.id;
      });

      console.log("Found user:", user);
      const profileImageUrl = user?.profile ? (user.profile.startsWith('http') ? user.profile : baseURL + user.profile) : "/AvatarBlank.jpg";

      if (user) {
        console.log("Setting profile data for user:", user);
        const profileData = {
          employmentId: user.employmentId || user.id || '',
          username: user.name || '',
          designation: user.designation || '',
          activity: user.activity || '',
          email: user.email || user.user_principal_name || '',
          extension: user.extension || '',
        };
        console.log("Profile data to set:", profileData);

        setProfileData(profileData);
        setProfileImage(`${profileImageUrl}?t=${Date.now()}`);
        setProfileImageKey(Date.now());
        setIsCustomImage(user.profile && !avatars.includes(user.profile.replace(baseURL, '')));
      } else {
        console.error("User not found in data array");
        console.log("Available user IDs:", result.data?.map(u => ({ id: u.id, name: u.name })));

        console.log("Trying fallback: using session data");
        if (session?.user) {
          const sessionUser = session.user;
          console.log("Session user data:", sessionUser);

          const fallbackProfileData = {
            employmentId: sessionUser.id || '',
            username: sessionUser.username || sessionUser.name || '',
            designation: '',
            activity: '',
            email: sessionUser.email || '',
            extension: '',
          };
          console.log("Fallback profile data:", fallbackProfileData);

          setProfileData(fallbackProfileData);
          setProfileImage("/AvatarBlank.jpg");
          setProfileImageKey(Date.now());
          setIsCustomImage(false);
          setErrorMessage("Using basic profile data. Some information may be missing.");
        } else {
          setErrorMessage("User not found in the system.");
        }
      }

      try {
        console.log("Fetching user reviews...");
        const reviewsResponse = await fetch(`${baseUrl}/v1/user-reviews`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${session.accessToken}`,
          },
        });

        if (reviewsResponse.ok) {
          const reviewsResult = await reviewsResponse.json();
          console.log("Reviews result:", reviewsResult);

          if (reviewsResult.status && reviewsResult.data) {
            const userReviews = reviewsResult.data.filter((review) => review.user_id == session.user.id);
            console.log("User reviews found:", userReviews.length);
            setRatingCount(userReviews.length);
          } else {
            console.warn("Reviews API returned unexpected format:", reviewsResult);
            setRatingCount(0);
          }
        } else {
          console.warn("Reviews API failed:", reviewsResponse.status, reviewsResponse.statusText);
          setRatingCount(0);
        }
      } catch (reviewError) {
        console.warn("Error fetching reviews (non-critical):", reviewError);
        setRatingCount(0);
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      setErrorMessage("Failed to fetch profile data: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (status === "authenticated") {
      fetchProfile();
    }
  }, [session, status]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prevData) => ({
      ...prevData,
      [name]: value || '',
    }));
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const imageURL = URL.createObjectURL(file);
      setProfileImage(imageURL);
      setUploadedFile(file);
      setIsCustomImage(true);
      setShowAvatars(false);
      setProfileImageKey(Date.now());
    }
  };

  const handleAvatarClick = (avatar) => {
    console.log("Avatar selected:", avatar);
    setProfileImage(avatar);
    setUploadedFile(null);
    setIsCustomImage(false);
    setShowAvatars(false);
    setProfileImageKey(Date.now());
    console.log("Avatar selection state updated - profileImage:", avatar, "isCustomImage:", false);
  };

  const handleUpdate = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      setErrorMessage("Please log in to update your profile.");
      return;
    }

    setLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      // Determine the correct profile value to send
      let profileValue;
      if (isCustomImage && uploadedFile) {
        // Custom uploaded image
        profileValue = uploadedFile;
      } else if (!isCustomImage && profileImage) {
        // Avatar selected - send just the filename without baseURL
        profileValue = profileImage.startsWith(baseURL)
          ? profileImage.replace(baseURL, '')
          : profileImage.startsWith('/')
            ? profileImage.substring(1) // Remove leading slash for avatars like '/gAvatar.svg'
            : profileImage;
      } else {
        // Fallback to current profile image
        profileValue = profileImage;
      }

      const formData = {
        id: session.user.id,
        name: profileData.username,
        designation: profileData.designation,
        email: profileData.email,
        extension: profileData.extension,
        activity: profileData.activity || '',
        role: isAdmin ? 'Admin' : 'User',
        status: 'active',
        profile: profileValue,
      };

      console.log("Update formData:", formData);
      console.log("Profile value being sent:", formData.profile);
      console.log("isCustomImage:", isCustomImage);
      console.log("uploadedFile:", uploadedFile);

      // Pass true for isImageChanged if we have a custom uploaded file
      const result = await updateUser(formData, isCustomImage && uploadedFile !== null, session.accessToken);
      console.log("Update result:", result);

      if (result?.error) {
        throw new Error(result.error);
      }

      if (result.code === 200 && result.data) {
        console.log("Update success, server data:", result.data);
        
        // Refetch profile data to ensure UI is in sync with server
        await fetchProfile();
        setSuccessMessage('Profile updated successfully!');
      } else {
        throw new Error(`Invalid response from server: ${JSON.stringify(result)}`);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setErrorMessage(`Failed to update profile: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (!session?.user?.id || !session?.accessToken) {
      setErrorMessage("Please log in to reset your profile.");
      return;
    }

    setLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      await fetchProfile();
      setSuccessMessage("Profile reset to current data.");
    } catch (error) {
      console.error("Error resetting profile:", error);
      setErrorMessage("Failed to reset profile: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Typography variant="h6" textAlign="center">
            Loading session...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Box
        sx={{
          background: theme.palette.background.secondary,
          borderRadius: '10px',
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
          padding: '20px',
          maxWidth: 900,
          margin: 'auto',
          overflowY: 'auto',
          maxHeight: '90vh',
          marginTop: '1px',
          '&::-webkit-scrollbar': { display: 'none' },
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
        }}
      >
        <Typography variant="h5" fontWeight="600" color="text.primary" gutterBottom>
          Edit and Update your profile
        </Typography>
        <Typography variant="h4" fontWeight="700" color="text.primary">
          My profile
        </Typography>

        <Divider sx={{ marginY: 2 }} />

        {errorMessage && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
            {errorMessage}
          </Alert>
        )}
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        )}
        {loading && (
          <Typography variant="h6" textAlign="center" sx={{ my: 2 }}>
            Loading...
          </Typography>
        )}

        <Grid container spacing={20}>
          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            {isCustomImage ? (
              <Avatar
                src={profileImage}
                alt="Profile"
                sx={{
                  width: 300,
                  height: 350,
                  borderRadius: '10px',
                  marginX: 'auto',
                  marginY: '10px',
                }}
              />
            ) : (
              <Box sx={{ position: 'relative', width: 300, height: 350, borderRadius: '10px', overflow: 'hidden', marginX: 'auto' }}>
                <Image
                  src={profileImage}
                  alt="Profile Avatar"
                  layout="fill"
                  objectFit="cover"
                  key={profileImageKey}
                />
              </Box>
            )}
            <DialogActions sx={{ justifyContent: 'flex-start', flexDirection: 'row', gap: 2, py: 1,  width: '200%', marginY: 2 }}>
              <Button
                variant="contained"
                color="secondary"
                component="label"
                sx={{
                  backgroundColor: "#A665E1 !important",
                  fontSize: '10px',
                  fontWeight: 800,
                  width: 120,
                }}
                disabled={loading}
              >
                Change Profile
                <input
                  type="file"
                  accept="image/*"
                  hidden
                  onChange={handleImageUpload}
                />
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={() => setShowAvatars((prev) => !prev)}
                sx={{
                  fontSize: '10px',
                  fontWeight: 800,
                  width: 120,
                }}
                disabled={loading}
              >
                Choose Avatar
              </Button>
            </DialogActions>

            {showAvatars && (
              <Box sx={{ display: 'flex', justifyContent: 'center', marginTop: 2 }}>
                {avatars.map((avatar, index) => (
                  <Box
                    key={index}
                    sx={{
                      width: 50,
                      height: 50,
                      borderRadius: '50%',
                      overflow: 'hidden',
                      cursor: 'pointer',
                      marginX: 1,
                    }}
                    onClick={() => handleAvatarClick(avatar)}
                  >
                    <Image src={avatar} alt={`Avatar ${index + 1}`} width={50} height={50} objectFit="cover" />
                  </Box>
                ))}
              </Box>
            )}
          </Grid>

          <Grid item xs={12} md={8}>
            <Box component="form">
              <TextField
                label="Employment ID"
                name="employmentId"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.employmentId || ''}
                onChange={handleInputChange}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="User name"
                name="username"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.username || ''}
                onChange={handleInputChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="Designation"
                name="designation"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.designation || ''}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              {isAdmin ? (
                <FormControl fullWidth sx={{ my: 2 }} disabled={loading}>
                  <InputLabel>Activity</InputLabel>
                  <Select
                    name="activity"
                    value={profileData.activity || ''}
                    label="Activity"
                    onChange={handleInputChange}
                  >
                    <MenuItem value="new_joiner">New Joiner</MenuItem>
                    <MenuItem value="cultural_ambassador">Cultural Ambassador</MenuItem>
                    <MenuItem value="">None</MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  label="Activity"
                  name="activity"
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  value={profileData.activity || ''}
                  disabled={true}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              )}
              <TextField
                label="Email"
                name="email"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.email || ''}
                onChange={handleInputChange}
                disabled={true}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label="Extension"
                name="extension"
                variant="outlined"
                fullWidth
                margin="normal"
                value={profileData?.extension || ''}
                onChange={handleInputChange}
                disabled={loading}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'flex-start',
                marginTop: 3,
                gap: 1,
              }}
            >
              <Typography sx={{ fontSize: '20px', fontWeight: 600 }}>
                {ratingCount}
              </Typography>
              <Image
                src={Rate}
                alt="Decoration"
                width={30}
                height={25}
              />
            </Box>
          </Grid>
        </Grid>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: 3,
            gap: 1,
          }}
        >
          <Button
            variant="outlined"
            color="secondary"
            onClick={handleReset}
            disabled={loading}
          >
            Reset
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{ backgroundColor: "#A665E1 !important" }}
            onClick={handleUpdate}
            disabled={loading}
          >
            Update
          </Button>
        </Box>
      </Box>
    </Dashboard>
  );
};

export default ProfileEdit;