// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   reactStrictMode: true,
//   images: {
//     domains: ['localhost','***************'],
//   },
// }

// module.exports = nextConfig
/** @type {import('next').NextConfig} */  
const nextConfig = {  
  reactStrictMode: true,  
  images: {  
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3009', // API server port
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3001', // Alternative API server port
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '', // For localhost without specific port
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '***************',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'hassana-api.360xpertsolutions.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'hassana-api.360xpertsolutions.com',
        port: '',
        pathname: '/**',
      },
    ],
  },  
};  

module.exports = nextConfig;
