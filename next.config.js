// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   reactStrictMode: true,
//   images: {
//     domains: ['localhost','***************'],
//   },
// }

// module.exports = nextConfig
/** @type {import('next').NextConfig} */  
const nextConfig = {  
  reactStrictMode: true,  
  images: {  
    remotePatterns: [  
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3009', // API server port
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '', // For localhost without specific port
        pathname: '/**',
      },
      {  
        protocol: 'http', // Ya 'https'  
        hostname: '***************',  
        port: '', // Ye khaali chhod sakte hain agar port specify nahi hai  
        pathname: '/**', // This allows any path, adjust as needed  
      }, 
      {
        protocol: 'https', // HTTPS for production API
        hostname: 'hassana-api.360xpertsolutions.com',
        port: '', // No port needed for HTTPS
        pathname: '/**', // This allows any path
      }, 
    ],  
  },  
};  

module.exports = nextConfig;
